<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}半托找图WEB版{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="{{ url_for('static', filename='css/主样式.css') }}" rel="stylesheet">

    <!-- 强制修复布局样式 -->
    <style>
        html, body {
            height: auto !important;
            min-height: 100vh !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        .container-fluid {
            height: auto !important;
            min-height: auto !important;
            padding: 0 !important;
        }
        .row {
            height: auto !important;
            margin: 0 !important;
        }
        .navbar {
            position: relative !important;
            z-index: 1000;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary" style="min-height: 56px;">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-search me-2"></i>半托找图WEB版
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item">
                    <span class="navbar-text" id="connection-status">
                        <i class="fas fa-circle text-success me-1"></i>已连接
                    </span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧控制面板 -->
            <div class="col-md-4 col-lg-3 bg-light border-end">
                <div class="p-3">
                    {% block left_panel %}
                    <!-- 默认左侧面板内容 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-cog me-2"></i>配置选项</h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">配置选项将在这里显示</p>
                        </div>
                    </div>
                    {% endblock %}
                </div>
            </div>

            <!-- 右侧主要工作区域 -->
            <div class="col-md-8 col-lg-9">
                <div class="p-3">
                    {% block main_content %}
                    <!-- 默认主要内容 -->
                    <div class="text-center mt-5">
                        <h3>欢迎使用半托找图WEB版</h3>
                        <p class="text-muted">请在左侧配置选项后开始使用</p>
                    </div>
                    {% endblock %}
                </div>
            </div>
        </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="fixed-bottom bg-white border-top">
        <div class="container-fluid">
            <div class="row py-2">
                <div class="col-md-6">
                    <small class="text-muted" id="status-text">就绪</small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted" id="progress-text"></small>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志模态框 -->
    <div class="modal fade" id="logModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-list-alt me-2"></i>操作日志
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="log-container" class="bg-dark text-light p-3 rounded" style="height: 400px; overflow-y: auto; font-family: 'Courier New', monospace;">
                        <!-- 日志内容将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="clearLogs()">
                        <i class="fas fa-trash me-1"></i>清空日志
                    </button>
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 进度模态框 -->
    <div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-spinner fa-spin me-2"></i>处理中...
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="progress">
                            <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div id="progress-message" class="text-center">
                        正在初始化...
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-primary" onclick="showLogs()">
                        <i class="fas fa-list-alt me-1"></i>查看日志
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/主脚本.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
