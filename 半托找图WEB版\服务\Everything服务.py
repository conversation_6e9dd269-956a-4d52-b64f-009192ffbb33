import requests
import json
from urllib.parse import quote
import os

class EverythingService:
    """Everything搜索服务"""
    
    def __init__(self):
        self.base_url = 'http://localhost:8080'
        self.image_url = 'http://127.0.0.1:8080'
    
    def update_config(self, base_url, image_url):
        """更新Everything服务配置"""
        self.base_url = base_url
        self.image_url = image_url
    
    def search_images(self, search_term, search_path="", strict_search=True):
        """搜索图片文件"""
        try:
            # 构建搜索查询
            search_query = search_term
            
            # 如果指定了搜索路径并启用了严格路径搜索
            if search_path and strict_search:
                if not search_path.endswith('\\'):
                    search_path += '\\'
                # 使用Everything的path:语法限制搜索范围
                search_query = f'path:"{search_path}" {search_query}'
            
            # EverythingSearch参数格式化
            search_params = {
                "search": search_query,
                "json": 1,
                "path_column": 1,
                "size_column": 1,
                "sort": "name",
                "ascending": 1
            }
            
            # 发送搜索请求
            response = requests.get(
                self.base_url,
                params=search_params,
                timeout=30
            )
            response.raise_for_status()
            data = response.json()
            
            # 处理搜索结果
            results = []
            for item in data.get("results", []):
                file_name = item.get('name', '')
                file_path = f"{item.get('path', '')}\\{file_name}".replace("\\\\", "\\")
                file_size = item.get('size', 0)
                
                # 检查是否为图片文件
                if self._is_image_file(file_name):
                    results.append({
                        'name': file_name,
                        'path': file_path,
                        'size': file_size,
                        'size_formatted': self._format_size(file_size),
                        'preview_url': f"/api/images/preview/{quote(file_path)}",
                        'download_url': f"{self.image_url}/{quote(file_path)}"
                    })
            
            return results
            
        except requests.RequestException as e:
            raise Exception(f"Everything API请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            raise Exception(f"Everything API响应解析失败: {str(e)}")
        except Exception as e:
            raise Exception(f"搜索图片时出错: {str(e)}")
    
    def search_files_by_sku(self, sku, search_path="", strict_search=True):
        """根据SKU搜索文件"""
        return self.search_images(sku, search_path, strict_search)
    
    def get_image_info(self, file_path):
        """获取图片信息"""
        try:
            # 通过Everything API获取图片
            image_url = f"{self.image_url}/{quote(file_path)}"
            
            response = requests.head(image_url, timeout=10)
            response.raise_for_status()
            
            # 获取文件信息
            content_length = response.headers.get('content-length', 0)
            content_type = response.headers.get('content-type', '')
            
            return {
                'path': file_path,
                'size': int(content_length) if content_length else 0,
                'content_type': content_type,
                'url': image_url
            }
            
        except Exception as e:
            raise Exception(f"获取图片信息失败: {str(e)}")
    
    def _is_image_file(self, filename):
        """检查是否为图片文件"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg'}
        file_ext = os.path.splitext(filename)[1].lower()
        return file_ext in image_extensions
    
    def _format_size(self, bytes_size):
        """格式化文件大小"""
        if bytes_size == 0:
            return "0 B"
        elif bytes_size < 1024:
            return f"{bytes_size} B"
        elif bytes_size < 1024 * 1024:
            return f"{bytes_size / 1024:.2f} KB"
        elif bytes_size < 1024 * 1024 * 1024:
            return f"{bytes_size / (1024 * 1024):.2f} MB"
        else:
            return f"{bytes_size / (1024 * 1024 * 1024):.2f} GB"
    
    def test_connection(self):
        """测试Everything服务连接"""
        try:
            response = requests.get(self.base_url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_search_statistics(self, search_results):
        """获取搜索结果统计信息"""
        if not search_results:
            return {
                'total_files': 0,
                'total_size': 0,
                'total_size_formatted': '0 B',
                'file_types': {}
            }
        
        total_size = sum(item.get('size', 0) for item in search_results)
        file_types = {}
        
        for item in search_results:
            ext = os.path.splitext(item.get('name', ''))[1].lower()
            file_types[ext] = file_types.get(ext, 0) + 1
        
        return {
            'total_files': len(search_results),
            'total_size': total_size,
            'total_size_formatted': self._format_size(total_size),
            'file_types': file_types
        }
