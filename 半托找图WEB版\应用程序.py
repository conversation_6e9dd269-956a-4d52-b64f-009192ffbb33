from flask import Flask, render_template, request, jsonify, send_file, session
from flask_socketio import SocketIO, emit
import os
import json
import configparser
from datetime import datetime
import threading
import queue
import requests
import re
from urllib.parse import quote
import io
from PIL import Image
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['DOWNLOAD_FOLDER'] = 'downloads'

# 初始化SocketIO
socketio = SocketIO(app, cors_allowed_origins="*")

# 确保必要的目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['DOWNLOAD_FOLDER'], exist_ok=True)

# 导入服务模块
from 服务.配置管理服务 import ConfigManager
from 服务.SKU提取服务 import SkuExtractor
from 服务.图片下载服务 import ImageDownloader
from 服务.Everything服务 import EverythingService

# 全局变量
config_manager = ConfigManager()
sku_extractor = SkuExtractor()
image_downloader = ImageDownloader()
everything_service = EverythingService()

@app.route('/')
def 主页():
    """主页路由"""
    return render_template('主页.html')

@app.route('/api/config', methods=['GET'])
def 获取配置():
    """获取当前配置"""
    try:
        config = config_manager.load_config()
        config_dict = {}
        for section in config.sections():
            config_dict[section] = dict(config[section])
        return jsonify({'success': True, 'data': config_dict})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/config', methods=['POST'])
def 更新配置():
    """更新配置"""
    try:
        data = request.json
        for section, options in data.items():
            for key, value in options.items():
                config_manager.update_config(section, key, value)
        
        socketio.emit('log', {'message': '配置已更新', 'type': 'success'})
        return jsonify({'success': True, 'message': '配置更新成功'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/sku/extract', methods=['POST'])
def 提取SKU():
    """从API提取SKU信息"""
    try:
        data = request.json
        api_url = data.get('api_url')
        cookie = data.get('cookie')
        
        if not cookie:
            return jsonify({'success': False, 'error': '请提供Cookie信息'})
        
        # 在后台线程中执行SKU提取
        thread = threading.Thread(target=执行SKU提取, args=(api_url, cookie))
        thread.daemon = True
        thread.start()
        
        return jsonify({'success': True, 'message': 'SKU提取任务已启动'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def 执行SKU提取(api_url, cookie):
    """执行SKU提取的后台任务"""
    try:
        socketio.emit('log', {'message': '开始提取SKU信息...', 'type': 'info'})
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Cookie': cookie,
            'Referer': 'https://www.dianxiaomi.com/'
        }
        
        response = requests.get(api_url, headers=headers, timeout=30)
        response.raise_for_status()
        
        skus = sku_extractor.extract_skus(response.text)
        
        socketio.emit('log', {'message': f'成功提取到 {len(skus)} 个SKU', 'type': 'success'})
        socketio.emit('sku_extracted', {'skus': skus})
        
    except Exception as e:
        socketio.emit('log', {'message': f'SKU提取失败: {str(e)}', 'type': 'error'})

@app.route('/api/images/search', methods=['POST'])
def 搜索图片():
    """搜索图片"""
    try:
        data = request.json
        search_terms = data.get('search_terms', [])
        search_path = data.get('search_path', '')
        
        if not search_terms:
            return jsonify({'success': False, 'error': '请提供搜索词'})
        
        # 在后台线程中执行图片搜索
        thread = threading.Thread(target=执行图片搜索, args=(search_terms, search_path))
        thread.daemon = True
        thread.start()
        
        return jsonify({'success': True, 'message': '图片搜索任务已启动'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def 执行图片搜索(search_terms, search_path):
    """执行图片搜索的后台任务"""
    try:
        socketio.emit('log', {'message': '开始搜索图片...', 'type': 'info'})
        
        results = []
        for term in search_terms:
            socketio.emit('log', {'message': f'搜索: {term}', 'type': 'info'})
            
            # 使用Everything服务搜索图片
            images = everything_service.search_images(term, search_path)
            results.append({
                'term': term,
                'images': images
            })
            
            socketio.emit('search_progress', {
                'current': len(results),
                'total': len(search_terms),
                'term': term,
                'found': len(images)
            })
        
        socketio.emit('log', {'message': '图片搜索完成', 'type': 'success'})
        socketio.emit('images_found', {'results': results})
        
    except Exception as e:
        socketio.emit('log', {'message': f'图片搜索失败: {str(e)}', 'type': 'error'})
        # 确保进度对话框被关闭
        socketio.emit('images_found', {'results': [], 'error': str(e)})

@app.route('/api/images/preview/<path:image_path>')
def 图片预览(image_path):
    """图片预览接口"""
    try:
        # 通过Everything API获取图片
        config = config_manager.load_config()
        image_url = f"{config['EVERYTHING']['image_url']}/{quote(image_path)}"
        
        response = requests.get(image_url, timeout=10)
        response.raise_for_status()
        
        # 创建图片对象并调整大小
        img = Image.open(io.BytesIO(response.content))
        img.thumbnail((400, 400), Image.LANCZOS)
        
        # 保存到内存
        img_io = io.BytesIO()
        img.save(img_io, 'JPEG', quality=85)
        img_io.seek(0)
        
        return send_file(img_io, mimetype='image/jpeg')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/images/download', methods=['POST'])
def 下载图片():
    """下载选中的图片"""
    try:
        data = request.json
        selected_images = data.get('selected_images', [])

        if not selected_images:
            return jsonify({'success': False, 'error': '请选择要下载的图片'})

        # 在后台线程中执行图片下载
        thread = threading.Thread(target=执行图片下载, args=(selected_images,))
        thread.daemon = True
        thread.start()

        return jsonify({'success': True, 'message': '图片下载任务已启动'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/sku/search', methods=['POST'])
def 搜索单个SKU():
    """搜索单个SKU的商品信息"""
    try:
        data = request.json
        sku = data.get('sku')
        cookie = data.get('cookie')

        if not sku or not cookie:
            return jsonify({'success': False, 'error': '请提供SKU和Cookie信息'})

        # 在后台线程中执行SKU搜索
        thread = threading.Thread(target=执行单个SKU搜索, args=(sku, cookie))
        thread.daemon = True
        thread.start()

        return jsonify({'success': True, 'message': 'SKU搜索任务已启动'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/sku/compare', methods=['POST'])
def SKU对比():
    """SKU对比功能"""
    try:
        data = request.json
        api_url = data.get('api_url')
        cookie = data.get('cookie')
        shared_folder = data.get('shared_folder')

        if not all([api_url, cookie, shared_folder]):
            return jsonify({'success': False, 'error': '请提供完整的配置信息'})

        # 在后台线程中执行SKU对比
        thread = threading.Thread(target=执行SKU对比, args=(api_url, cookie, shared_folder))
        thread.daemon = True
        thread.start()

        return jsonify({'success': True, 'message': 'SKU对比任务已启动'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/sku/download', methods=['POST'])
def 下载SKU图片():
    """下载SKU对应的图片"""
    try:
        data = request.json
        skus = data.get('skus', [])
        search_path = data.get('search_path', '')
        cookie = data.get('cookie')

        if not skus:
            return jsonify({'success': False, 'error': '请提供SKU列表'})

        if not cookie:
            return jsonify({'success': False, 'error': '请提供Cookie信息'})

        # 在后台线程中执行SKU图片下载
        thread = threading.Thread(target=执行SKU图片下载, args=(skus, search_path, cookie))
        thread.daemon = True
        thread.start()

        return jsonify({'success': True, 'message': 'SKU图片下载任务已启动'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})



def 执行图片下载(selected_images):
    """执行图片下载的后台任务"""
    try:
        socketio.emit('log', {'message': '开始下载图片...', 'type': 'info'})
        
        # 创建下载目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        download_dir = os.path.join(app.config['DOWNLOAD_FOLDER'], f'图片下载_{timestamp}')
        os.makedirs(download_dir, exist_ok=True)
        
        success_count = 0
        total_count = len(selected_images)
        
        for idx, image_info in enumerate(selected_images, 1):
            try:
                socketio.emit('download_progress', {
                    'current': idx,
                    'total': total_count,
                    'filename': image_info.get('name', '未知文件')
                })
                
                # 下载图片
                success = image_downloader.download_image(
                    image_info['url'],
                    os.path.join(download_dir, image_info['filename'])
                )
                
                if success:
                    success_count += 1
                    socketio.emit('log', {
                        'message': f'下载成功: {image_info["filename"]}',
                        'type': 'success'
                    })
                else:
                    socketio.emit('log', {
                        'message': f'下载失败: {image_info["filename"]}',
                        'type': 'error'
                    })
                
            except Exception as e:
                socketio.emit('log', {
                    'message': f'下载出错: {image_info.get("filename", "未知文件")} - {str(e)}',
                    'type': 'error'
                })
        
        socketio.emit('log', {
            'message': f'下载完成! 成功: {success_count}/{total_count}',
            'type': 'success'
        })
        socketio.emit('download_complete', {
            'success_count': success_count,
            'total_count': total_count,
            'download_dir': download_dir
        })
        
    except Exception as e:
        socketio.emit('log', {'message': f'下载任务失败: {str(e)}', 'type': 'error'})

def 执行单个SKU搜索(sku, cookie):
    """执行单个SKU搜索的后台任务"""
    try:
        socketio.emit('log', {'message': f'开始搜索SKU: {sku}', 'type': 'info'})

        config = config_manager.load_config()
        api_url = config['API']['sku_search_url']

        result, error = sku_extractor.search_sku_product_info(sku, cookie, api_url)

        if result:
            socketio.emit('log', {'message': f'找到商品: {result["product_name"]}', 'type': 'success'})
            socketio.emit('sku_info_found', {
                'sku': sku,
                'product_name': result['product_name'],
                'thumb_url': result['thumb_url']
            })
        else:
            socketio.emit('log', {'message': f'SKU搜索失败: {error}', 'type': 'error'})

    except Exception as e:
        socketio.emit('log', {'message': f'SKU搜索出错: {str(e)}', 'type': 'error'})

def 执行SKU对比(api_url, cookie, shared_folder):
    """执行SKU对比的后台任务"""
    try:
        socketio.emit('log', {'message': '开始SKU对比...', 'type': 'info'})

        # 从API获取SKU列表
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Cookie': cookie,
            'Referer': 'https://www.dianxiaomi.com/'
        }

        response = requests.get(api_url, headers=headers, timeout=30)
        response.raise_for_status()

        api_skus = sku_extractor.extract_skus(response.text)
        socketio.emit('log', {'message': f'从API获取到 {len(api_skus)} 个SKU', 'type': 'info'})

        # 获取本地文件夹中的SKU
        import os
        local_skus = []
        if os.path.exists(shared_folder):
            for filename in os.listdir(shared_folder):
                sku = os.path.splitext(filename)[0]
                local_skus.append(sku)

        socketio.emit('log', {'message': f'从本地文件夹获取到 {len(local_skus)} 个SKU', 'type': 'info'})

        # 执行对比
        compare_result = sku_extractor.compare_skus(api_skus, local_skus)

        socketio.emit('log', {'message': f'对比完成，缺失 {compare_result["missing_count"]} 个SKU', 'type': 'success'})
        socketio.emit('sku_compare_complete', {'data': compare_result})

    except Exception as e:
        socketio.emit('log', {'message': f'SKU对比失败: {str(e)}', 'type': 'error'})
        # 发送错误完成事件，确保前端进度对话框被关闭
        socketio.emit('sku_compare_complete', {'error': str(e)})

def 执行SKU图片下载(skus, search_path, cookie):
    """执行SKU图片下载的后台任务"""
    try:
        socketio.emit('log', {'message': f'开始下载 {len(skus)} 个SKU的图片', 'type': 'info'})

        config = config_manager.load_config()
        api_url = config['API']['sku_search_url']

        # 创建下载目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        download_dir = os.path.join(app.config['DOWNLOAD_FOLDER'], f'SKU图片_{timestamp}')
        os.makedirs(download_dir, exist_ok=True)

        success_count = 0
        for idx, sku in enumerate(skus, 1):
            try:
                socketio.emit('download_progress', {
                    'current': idx,
                    'total': len(skus),
                    'filename': f'SKU: {sku}'
                })

                # 搜索SKU商品信息
                result, error = sku_extractor.search_sku_product_info(sku, cookie, api_url)

                if result and result['thumb_url']:
                    # 下载商品图片
                    image_path = os.path.join(download_dir, f"{sku}.jpg")
                    success = image_downloader.download_image(result['thumb_url'], image_path)

                    if success:
                        success_count += 1
                        socketio.emit('log', {'message': f'下载成功: {sku}', 'type': 'success'})
                    else:
                        socketio.emit('log', {'message': f'下载失败: {sku}', 'type': 'error'})
                else:
                    socketio.emit('log', {'message': f'未找到图片: {sku}', 'type': 'warning'})

            except Exception as e:
                socketio.emit('log', {'message': f'处理SKU {sku} 出错: {str(e)}', 'type': 'error'})

        socketio.emit('log', {'message': f'SKU图片下载完成! 成功: {success_count}/{len(skus)}', 'type': 'success'})
        socketio.emit('download_complete', {
            'success_count': success_count,
            'total_count': len(skus),
            'download_dir': download_dir
        })

    except Exception as e:
        socketio.emit('log', {'message': f'SKU图片下载失败: {str(e)}', 'type': 'error'})



@socketio.on('connect')
def 处理连接():
    """处理WebSocket连接"""
    emit('log', {'message': '已连接到服务器', 'type': 'info'})

@socketio.on('disconnect')
def 处理断开():
    """处理WebSocket断开"""
    print('客户端断开连接')

if __name__ == '__main__':
    print("正在启动应用程序...")
    print(f"Flask应用已创建: {app}")
    print(f"SocketIO已初始化: {socketio}")
    try:
        socketio.run(app, debug=True, host='127.0.0.1', port=5714)
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
