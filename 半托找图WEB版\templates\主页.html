{% extends "基础模板.html" %}

{% block title %}主页 - 半托找图WEB版{% endblock %}

{% block left_panel %}
<!-- 配置选项卡 -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-cog me-2"></i>API配置</h6>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <label class="form-label">API地址</label>
            <input type="url" class="form-control form-control-sm" id="api-url" 
                   placeholder="输入API地址">
        </div>
        <div class="mb-3">
            <label class="form-label">Cookie</label>
            <textarea class="form-control form-control-sm" id="api-cookie" rows="3" 
                      placeholder="输入Cookie信息"></textarea>
        </div>
        <button class="btn btn-primary btn-sm w-100" onclick="saveApiConfig()">
            <i class="fas fa-save me-1"></i>保存配置
        </button>
    </div>
</div>

<!-- 搜索配置 -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-search me-2"></i>搜索配置</h6>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <label class="form-label">搜索路径</label>
            <input type="text" class="form-control form-control-sm" id="search-path" 
                   placeholder="E:\图片\原图">
        </div>
        <div class="mb-3">
            <label class="form-label">目标路径后缀</label>
            <div class="input-group">
                <input type="text" class="form-control form-control-sm" id="target-suffix" 
                       placeholder="\导出图\已完成">
                <div class="input-group-text">
                    <input class="form-check-input" type="checkbox" id="enable-suffix" checked>
                </div>
            </div>
        </div>
        <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" id="strict-search" checked>
            <label class="form-check-label">严格搜索路径限制</label>
        </div>
        <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" id="ignore-chars">
            <label class="form-check-label">忽略文件名前后字符</label>
        </div>
        <div class="row" id="ignore-chars-config" style="display: none;">
            <div class="col-6">
                <label class="form-label">忽略前</label>
                <input type="number" class="form-control form-control-sm" id="ignore-prefix" value="20" min="0">
            </div>
            <div class="col-6">
                <label class="form-label">忽略后</label>
                <input type="number" class="form-control form-control-sm" id="ignore-suffix" value="50" min="0">
            </div>
        </div>
    </div>
</div>

<!-- Everything配置 -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-server me-2"></i>Everything配置</h6>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <label class="form-label">Everything API地址</label>
            <input type="url" class="form-control form-control-sm" id="everything-url" 
                   value="http://localhost:8080">
        </div>
        <div class="mb-3">
            <label class="form-label">图片服务地址</label>
            <input type="url" class="form-control form-control-sm" id="everything-image-url" 
                   value="http://127.0.0.1:8080">
        </div>
        <button class="btn btn-outline-primary btn-sm w-100" onclick="testEverythingConnection()">
            <i class="fas fa-plug me-1"></i>测试连接
        </button>
    </div>
</div>

<!-- 共享文件夹配置 -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-folder-open me-2"></i>共享文件夹</h6>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <label class="form-label">共享文件夹路径</label>
            <input type="text" class="form-control form-control-sm" id="shared-folder" 
                   placeholder="\\*************\图库">
        </div>
    </div>
</div>
{% endblock %}

{% block main_content %}
<!-- 功能选项卡 -->
<ul class="nav nav-tabs mb-3" id="mainTabs">
    <li class="nav-item">
        <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#sku-compare">
            <i class="fas fa-balance-scale me-1"></i>SKU对比
        </button>
    </li>
    <li class="nav-item">
        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#sku-extract">
            <i class="fas fa-download me-1"></i>SKU提取
        </button>
    </li>
    <li class="nav-item">
        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#image-search">
            <i class="fas fa-images me-1"></i>图片搜索
        </button>
    </li>
</ul>

<!-- 选项卡内容 -->
<div class="tab-content">
    <!-- SKU对比选项卡 -->
    <div class="tab-pane fade show active" id="sku-compare">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-balance-scale me-2"></i>SKU对比</h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <button class="btn btn-warning btn-lg" onclick="compareSkus()">
                        <i class="fas fa-balance-scale me-2"></i>开始SKU对比
                    </button>
                </div>

                <div class="mt-4" id="compare-results" style="display: none;">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-primary" id="api-count">0</h5>
                                    <p class="card-text">API中的SKU</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success" id="local-count">0</h5>
                                    <p class="card-text">本地文件夹中的SKU</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-danger" id="missing-count">0</h5>
                                    <p class="card-text">缺失的SKU</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <button class="btn btn-success" onclick="downloadMissingSkus()">
                            <i class="fas fa-download me-1"></i>下载缺失的SKU图片
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SKU提取选项卡 -->
    <div class="tab-pane fade" id="sku-extract">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-download me-2"></i>SKU提取</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>单个SKU查询</h6>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="single-sku" placeholder="输入SKU编号">
                            <button class="btn btn-outline-primary" onclick="searchSingleSku()">
                                <i class="fas fa-search me-1"></i>查询
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>批量SKU提取</h6>
                        <button class="btn btn-success" onclick="extractAllSkus()">
                            <i class="fas fa-download me-1"></i>从API提取所有SKU
                        </button>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h6>SKU列表</h6>
                    <textarea class="form-control" id="sku-list" rows="10" 
                              placeholder="SKU列表将在这里显示，每行一个SKU"></textarea>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary me-2" onclick="downloadSkuImages()">
                        <i class="fas fa-download me-1"></i>下载SKU图片
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearSkuList()">
                        <i class="fas fa-trash me-1"></i>清空列表
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图片搜索选项卡 -->
    <div class="tab-pane fade" id="image-search">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-images me-2"></i>图片搜索</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">搜索关键词</label>
                    <textarea class="form-control" id="search-terms" rows="5" 
                              placeholder="输入搜索关键词，每行一个"></textarea>
                </div>
                
                <div class="mb-3">
                    <button class="btn btn-primary me-2" onclick="searchImages()">
                        <i class="fas fa-search me-1"></i>开始搜索
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearSearchResults()">
                        <i class="fas fa-trash me-1"></i>清空结果
                    </button>
                </div>
                
                <!-- 搜索结果区域 -->
                <div id="search-results" class="mt-4">
                    <!-- 搜索结果将在这里显示 -->
                </div>
            </div>
        </div>
    </div>


</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/主页脚本.js') }}"></script>
{% endblock %}
