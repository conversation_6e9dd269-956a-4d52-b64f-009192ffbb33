// 主页专用脚本 - 具体功能实现

// 全局变量
let selectedImages = [];
let searchResults = [];
let currentSkus = [];

// SKU相关功能
function searchSingleSku() {
    const sku = document.getElementById('single-sku').value.trim();
    if (!sku) {
        showError('请输入SKU编号');
        return;
    }
    
    const cookie = document.getElementById('api-cookie').value.trim();
    if (!cookie) {
        showError('请先配置Cookie信息');
        return;
    }
    
    showProgressModal('正在查询SKU信息...');
    addLog(`开始查询SKU: ${sku}`, 'info');
    
    // 发送查询请求
    fetch('/api/sku/search', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            sku: sku,
            cookie: cookie
        })
    })
    .then(response => response.json())
    .then(data => {
        hideProgressModal();
        if (data.success) {
            // 将SKU添加到列表
            addSkuToList(sku);
            showSuccess(`SKU ${sku} 查询成功`);
        } else {
            showError('SKU查询失败: ' + data.error);
        }
    })
    .catch(error => {
        hideProgressModal();
        showError('SKU查询失败: ' + error.message);
    });
}

function extractAllSkus() {
    const apiUrl = document.getElementById('api-url').value.trim();
    const cookie = document.getElementById('api-cookie').value.trim();
    
    if (!apiUrl || !cookie) {
        showError('请先配置API地址和Cookie信息');
        return;
    }
    
    showProgressModal('正在提取SKU信息...');
    
    fetch('/api/sku/extract', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            api_url: apiUrl,
            cookie: cookie
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLog('SKU提取任务已启动', 'info');
        } else {
            hideProgressModal();
            showError('SKU提取失败: ' + data.error);
        }
    })
    .catch(error => {
        hideProgressModal();
        showError('SKU提取失败: ' + error.message);
    });
}

function displayExtractedSkus(skus) {
    currentSkus = skus;
    const skuList = document.getElementById('sku-list');
    skuList.value = skus.join('\n');
    
    showSuccess(`成功提取到 ${skus.length} 个SKU`);
    addLog(`SKU提取完成，共 ${skus.length} 个`, 'success');
}

function addSkuToList(sku) {
    const skuList = document.getElementById('sku-list');
    const currentSkus = skuList.value.split('\n').filter(s => s.trim());
    
    if (!currentSkus.includes(sku)) {
        currentSkus.push(sku);
        skuList.value = currentSkus.join('\n');
    }
}

function clearSkuList() {
    document.getElementById('sku-list').value = '';
    currentSkus = [];
    addLog('SKU列表已清空', 'info');
}

function downloadSkuImages() {
    const skuText = document.getElementById('sku-list').value.trim();
    if (!skuText) {
        showError('请先输入或提取SKU列表');
        return;
    }
    
    const skus = skuText.split('\n').filter(s => s.trim());
    if (skus.length === 0) {
        showError('SKU列表为空');
        return;
    }
    
    showProgressModal('正在下载SKU图片...');
    
    fetch('/api/sku/download', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            skus: skus,
            search_path: document.getElementById('search-path').value,
            cookie: document.getElementById('api-cookie').value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLog('SKU图片下载任务已启动', 'info');
        } else {
            hideProgressModal();
            showError('SKU图片下载失败: ' + data.error);
        }
    })
    .catch(error => {
        hideProgressModal();
        showError('SKU图片下载失败: ' + error.message);
    });
}

// 图片搜索功能
function searchImages() {
    const searchTermsText = document.getElementById('search-terms').value.trim();
    if (!searchTermsText) {
        showError('请输入搜索关键词');
        return;
    }

    const searchTerms = searchTermsText.split('\n').filter(term => term.trim());
    if (searchTerms.length === 0) {
        showError('搜索关键词为空');
        return;
    }

    console.log('开始搜索图片，搜索词:', searchTerms);
    showProgressModal('正在搜索图片...');

    // 设置超时保护，60秒后自动关闭进度对话框
    const searchTimeout = setTimeout(() => {
        console.log('搜索超时，自动关闭进度对话框');
        hideProgressModal();
        showError('搜索超时，请检查Everything服务是否正常运行');
        addLog('搜索超时', 'error');
    }, 60000);

    // 保存超时ID，以便在搜索完成时清除
    window.currentSearchTimeout = searchTimeout;
    console.log('设置搜索超时保护，超时ID:', searchTimeout);

    fetch('/api/images/search', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            search_terms: searchTerms,
            search_path: document.getElementById('search-path').value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLog('图片搜索任务已启动', 'info');
        } else {
            clearTimeout(window.currentSearchTimeout);
            hideProgressModal();
            showError('图片搜索失败: ' + data.error);
        }
    })
    .catch(error => {
        clearTimeout(window.currentSearchTimeout);
        hideProgressModal();
        showError('图片搜索失败: ' + error.message);
    });
}

function displaySearchResults(results) {
    searchResults = results;
    const container = document.getElementById('search-results');
    container.innerHTML = '';
    
    results.forEach((result, index) => {
        const resultDiv = document.createElement('div');
        resultDiv.className = 'search-result-item';
        
        const header = document.createElement('div');
        header.className = 'search-result-header';
        header.textContent = `搜索词: ${result.term}`;
        
        const stats = document.createElement('div');
        stats.className = 'search-result-stats';
        stats.textContent = `找到 ${result.images.length} 张图片`;
        
        const imageGrid = document.createElement('div');
        imageGrid.className = 'image-grid';
        
        result.images.forEach((image, imgIndex) => {
            const imagePreview = createImagePreview(image, index, imgIndex);
            imageGrid.appendChild(imagePreview);
        });
        
        resultDiv.appendChild(header);
        resultDiv.appendChild(stats);
        resultDiv.appendChild(imageGrid);
        container.appendChild(resultDiv);
    });
    
    // 添加批量操作按钮
    if (results.length > 0) {
        const actionDiv = document.createElement('div');
        actionDiv.className = 'mt-3 text-center';
        actionDiv.innerHTML = `
            <button class="btn btn-primary me-2" onclick="selectAllImages()">
                <i class="fas fa-check-square me-1"></i>全选
            </button>
            <button class="btn btn-outline-secondary me-2" onclick="clearSelection()">
                <i class="fas fa-square me-1"></i>清空选择
            </button>
            <button class="btn btn-success" onclick="downloadSelectedImages()">
                <i class="fas fa-download me-1"></i>下载选中图片 (<span id="selected-count">0</span>)
            </button>
        `;
        container.appendChild(actionDiv);
    }
}

function createImagePreview(image, resultIndex, imageIndex) {
    const preview = document.createElement('div');
    preview.className = 'image-preview';
    preview.dataset.resultIndex = resultIndex;
    preview.dataset.imageIndex = imageIndex;
    
    const img = document.createElement('img');
    img.src = image.preview_url;
    img.alt = image.name;
    img.title = `${image.name} (${image.size_formatted})`;
    
    const overlay = document.createElement('div');
    overlay.className = 'overlay';
    overlay.innerHTML = `
        <div class="text-center">
            <i class="fas fa-check-circle fa-2x mb-2"></i>
            <div>${image.name}</div>
            <div>${image.size_formatted}</div>
        </div>
    `;
    
    preview.appendChild(img);
    preview.appendChild(overlay);
    
    preview.addEventListener('click', function() {
        toggleImageSelection(this, image);
    });
    
    return preview;
}

function toggleImageSelection(element, image) {
    const isSelected = element.classList.contains('selected');
    
    if (isSelected) {
        element.classList.remove('selected');
        selectedImages = selectedImages.filter(img => img.path !== image.path);
    } else {
        element.classList.add('selected');
        selectedImages.push(image);
    }
    
    updateSelectedCount();
}

function selectAllImages() {
    selectedImages = [];
    const previews = document.querySelectorAll('.image-preview');
    
    previews.forEach(preview => {
        preview.classList.add('selected');
        const resultIndex = parseInt(preview.dataset.resultIndex);
        const imageIndex = parseInt(preview.dataset.imageIndex);
        const image = searchResults[resultIndex].images[imageIndex];
        selectedImages.push(image);
    });
    
    updateSelectedCount();
}

function clearSelection() {
    selectedImages = [];
    const previews = document.querySelectorAll('.image-preview');
    previews.forEach(preview => {
        preview.classList.remove('selected');
    });
    updateSelectedCount();
}

function updateSelectedCount() {
    const countElement = document.getElementById('selected-count');
    if (countElement) {
        countElement.textContent = selectedImages.length;
    }
}

function downloadSelectedImages() {
    if (selectedImages.length === 0) {
        showError('请先选择要下载的图片');
        return;
    }
    
    showProgressModal('正在下载图片...');
    
    // 准备下载数据
    const downloadData = selectedImages.map(image => ({
        url: image.download_url,
        filename: image.name,
        path: image.path
    }));
    
    fetch('/api/images/download', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            selected_images: downloadData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLog('图片下载任务已启动', 'info');
        } else {
            hideProgressModal();
            showError('图片下载失败: ' + data.error);
        }
    })
    .catch(error => {
        hideProgressModal();
        showError('图片下载失败: ' + error.message);
    });
}

function clearSearchResults() {
    document.getElementById('search-results').innerHTML = '';
    selectedImages = [];
    searchResults = [];
    addLog('搜索结果已清空', 'info');
}

// SKU对比功能
function compareSkus() {
    const cookie = document.getElementById('api-cookie').value.trim();
    const sharedFolder = document.getElementById('shared-folder').value.trim();
    
    if (!cookie) {
        showError('请先配置Cookie信息');
        return;
    }
    
    if (!sharedFolder) {
        showError('请先配置共享文件夹路径');
        return;
    }
    
    showProgressModal('正在进行SKU对比...');
    
    fetch('/api/sku/compare', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            api_url: document.getElementById('api-url').value,
            cookie: cookie,
            shared_folder: sharedFolder
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            hideProgressModal();
            showError('SKU对比失败: ' + data.error);
        }
        // 成功时不隐藏进度对话框，等待WebSocket事件
    })
    .catch(error => {
        hideProgressModal();
        showError('SKU对比失败: ' + error.message);
    });
}

function displayCompareResults(data) {
    document.getElementById('api-count').textContent = data.api_count;
    document.getElementById('local-count').textContent = data.local_count;
    document.getElementById('missing-count').textContent = data.missing_count;
    
    document.getElementById('compare-results').style.display = 'block';
    
    // 将缺失的SKU显示在SKU列表中
    if (data.missing_skus && data.missing_skus.length > 0) {
        document.getElementById('sku-list').value = data.missing_skus.join('\n');
        currentSkus = data.missing_skus;
    }
    
    showSuccess(`SKU对比完成! 缺失 ${data.missing_count} 个SKU`);
}

function downloadMissingSkus() {
    const skuText = document.getElementById('sku-list').value.trim();
    if (!skuText) {
        showError('没有缺失的SKU需要下载');
        return;
    }
    
    downloadSkuImages();
}


