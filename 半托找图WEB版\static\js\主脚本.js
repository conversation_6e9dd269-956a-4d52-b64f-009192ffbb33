// 主脚本文件 - 全局功能和WebSocket连接

// 全局变量
let socket = null;
let isConnected = false;
let currentConfig = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeSocket();
    loadConfiguration();
    bindEventListeners();
});

// 初始化WebSocket连接
function initializeSocket() {
    socket = io();
    
    socket.on('connect', function() {
        isConnected = true;
        updateConnectionStatus(true);
        addLog('已连接到服务器', 'success');
    });
    
    socket.on('disconnect', function() {
        isConnected = false;
        updateConnectionStatus(false);
        addLog('与服务器断开连接', 'error');
    });
    
    socket.on('log', function(data) {
        addLog(data.message, data.type);
    });
    
    socket.on('sku_extracted', function(data) {
        displayExtractedSkus(data.skus);
        hideProgressModal();
    });
    
    socket.on('images_found', function(data) {
        displaySearchResults(data.results);
        hideProgressModal();
    });
    
    socket.on('search_progress', function(data) {
        updateProgress(data.current, data.total, `搜索: ${data.term} (找到 ${data.found} 张图片)`);
    });
    
    socket.on('download_progress', function(data) {
        updateProgress(data.current, data.total, `下载: ${data.filename}`);
    });
    
    socket.on('download_complete', function(data) {
        addLog(`下载完成! 成功: ${data.success_count}/${data.total_count}`, 'success');
        hideProgressModal();
        updateDownloadStats(data);
    });

    socket.on('sku_compare_complete', function(data) {
        hideProgressModal();
        if (data.error) {
            showError('SKU对比失败: ' + data.error);
        } else if (data.data) {
            displayCompareResults(data.data);
        }
    });
}

// 更新连接状态
function updateConnectionStatus(connected) {
    const statusElement = document.getElementById('connection-status');
    if (connected) {
        statusElement.innerHTML = '<i class="fas fa-circle text-success me-1"></i>已连接';
    } else {
        statusElement.innerHTML = '<i class="fas fa-circle text-danger me-1"></i>已断开';
    }
}

// 加载配置
function loadConfiguration() {
    fetch('/api/config')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentConfig = data.data;
                populateConfigFields();
            }
        })
        .catch(error => {
            console.error('加载配置失败:', error);
            addLog('加载配置失败: ' + error.message, 'error');
        });
}

// 填充配置字段
function populateConfigFields() {
    if (currentConfig.API) {
        document.getElementById('api-url').value = currentConfig.API.url || '';
        document.getElementById('api-cookie').value = currentConfig.API.cookie || '';
    }
    
    if (currentConfig.SEARCH) {
        document.getElementById('search-path').value = currentConfig.SEARCH.base_path || '';
        document.getElementById('target-suffix').value = currentConfig.SEARCH.target_suffix || '';
        document.getElementById('enable-suffix').checked = currentConfig.SEARCH.enable_target_suffix === 'True';
        document.getElementById('ignore-chars').checked = currentConfig.SEARCH.ignore_filename_chars === 'True';
        document.getElementById('ignore-prefix').value = currentConfig.SEARCH.ignore_prefix_count || '20';
        document.getElementById('ignore-suffix').value = currentConfig.SEARCH.ignore_suffix_count || '50';
    }
    
    if (currentConfig.EVERYTHING) {
        document.getElementById('everything-url').value = currentConfig.EVERYTHING.base_url || '';
        document.getElementById('everything-image-url').value = currentConfig.EVERYTHING.image_url || '';
    }
    
    if (currentConfig.SHARED) {
        document.getElementById('shared-folder').value = currentConfig.SHARED.folder || '';
    }
    
    if (currentConfig.OPTIONS) {
        document.getElementById('strict-search').checked = currentConfig.OPTIONS.strict_search === 'True';
    }
}

// 绑定事件监听器
function bindEventListeners() {
    // 忽略字符选项切换
    document.getElementById('ignore-chars').addEventListener('change', function() {
        const configDiv = document.getElementById('ignore-chars-config');
        configDiv.style.display = this.checked ? 'block' : 'none';
    });
    
    // 配置字段自动保存
    const configFields = [
        'api-url', 'api-cookie', 'search-path', 'target-suffix', 
        'everything-url', 'everything-image-url', 'shared-folder',
        'ignore-prefix', 'ignore-suffix'
    ];
    
    configFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('blur', saveConfiguration);
        }
    });
    
    // 复选框配置自动保存
    const checkboxFields = ['enable-suffix', 'strict-search', 'ignore-chars'];
    checkboxFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('change', saveConfiguration);
        }
    });
}

// 保存API配置
function saveApiConfig() {
    saveConfiguration();
    addLog('API配置已保存', 'success');
}

// 保存配置
function saveConfiguration() {
    const config = {
        API: {
            url: document.getElementById('api-url').value,
            cookie: document.getElementById('api-cookie').value,
            base_url: 'https://www.dianxiaomi.com',
            sku_search_url: 'https://www.dianxiaomi.com/api/popTemuProduct/pageList.json',
            referer: 'https://www.dianxiaomi.com/'
        },
        SEARCH: {
            base_path: document.getElementById('search-path').value,
            target_suffix: document.getElementById('target-suffix').value,
            enable_target_suffix: document.getElementById('enable-suffix').checked.toString(),
            ignore_filename_chars: document.getElementById('ignore-chars').checked.toString(),
            ignore_prefix_count: document.getElementById('ignore-prefix').value,
            ignore_suffix_count: document.getElementById('ignore-suffix').value
        },
        EVERYTHING: {
            base_url: document.getElementById('everything-url').value,
            image_url: document.getElementById('everything-image-url').value,
            search_path: document.getElementById('search-path').value,
            target_suffix: document.getElementById('target-suffix').value
        },
        SHARED: {
            folder: document.getElementById('shared-folder').value
        },
        OPTIONS: {
            strict_search: document.getElementById('strict-search').checked.toString()
        },
        HEADERS: {
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            accept: 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            accept_encoding: 'gzip, deflate, br',
            accept_language: 'zh-CN,zh;q=0.9,en;q=0.8',
            connection: 'keep-alive'
        }
    };
    
    fetch('/api/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentConfig = config;
        } else {
            addLog('保存配置失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('保存配置失败:', error);
        addLog('保存配置失败: ' + error.message, 'error');
    });
}

// 测试Everything连接
function testEverythingConnection() {
    const url = document.getElementById('everything-url').value;
    if (!url) {
        addLog('请先输入Everything API地址', 'warning');
        return;
    }
    
    addLog('正在测试Everything连接...', 'info');
    
    fetch(url, { method: 'GET', timeout: 5000 })
        .then(response => {
            if (response.ok) {
                addLog('Everything连接测试成功', 'success');
            } else {
                addLog('Everything连接测试失败: HTTP ' + response.status, 'error');
            }
        })
        .catch(error => {
            addLog('Everything连接测试失败: ' + error.message, 'error');
        });
}

// 添加日志
function addLog(message, type = 'info') {
    const logContainer = document.getElementById('log-container');
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.innerHTML = `[${timestamp}] ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
    
    // 更新状态栏
    updateStatusText(message);
}

// 清空日志
function clearLogs() {
    document.getElementById('log-container').innerHTML = '';
}

// 显示日志模态框
function showLogs() {
    const modal = new bootstrap.Modal(document.getElementById('logModal'));
    modal.show();
}

// 显示进度模态框
function showProgressModal(title = '处理中...') {
    const modal = document.getElementById('progressModal');
    modal.querySelector('.modal-title').innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${title}`;
    const progressModal = new bootstrap.Modal(modal, { backdrop: 'static' });
    progressModal.show();
}

// 隐藏进度模态框
function hideProgressModal() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('progressModal'));
    if (modal) {
        modal.hide();
    }
}

// 更新进度
function updateProgress(current, total, message) {
    const percentage = Math.round((current / total) * 100);
    const progressBar = document.getElementById('progress-bar');
    const progressMessage = document.getElementById('progress-message');
    
    progressBar.style.width = percentage + '%';
    progressBar.textContent = percentage + '%';
    progressMessage.textContent = message;
}

// 更新状态文本
function updateStatusText(text) {
    document.getElementById('status-text').textContent = text;
}

// 更新进度文本
function updateProgressText(text) {
    document.getElementById('progress-text').textContent = text;
}

// 显示错误消息
function showError(message) {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0';
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', () => {
        document.body.removeChild(toast);
    });
}

// 显示成功消息
function showSuccess(message) {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0';
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', () => {
        document.body.removeChild(toast);
    });
}

// 工具函数：格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// SKU对比结果显示函数
function displayCompareResults(data) {
    document.getElementById('api-count').textContent = data.api_count;
    document.getElementById('local-count').textContent = data.local_count;
    document.getElementById('missing-count').textContent = data.missing_count;

    document.getElementById('compare-results').style.display = 'block';

    // 将缺失的SKU显示在SKU列表中
    if (data.missing_skus && data.missing_skus.length > 0) {
        document.getElementById('sku-list').value = data.missing_skus.join('\n');
        // 更新全局变量（如果存在）
        if (typeof currentSkus !== 'undefined') {
            currentSkus = data.missing_skus;
        }
    }

    showSuccess(`SKU对比完成! 缺失 ${data.missing_count} 个SKU`);
}
